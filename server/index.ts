/* eslint-disable @typescript-eslint/no-explicit-any */
import express, { Request, Response } from 'express';
import cors from 'cors';
import dotenv from 'dotenv';
import axios from 'axios';
import fs from 'fs';
import path from 'path';
import { v4 as uuidv4 } from 'uuid';

// =================================================================
// JOB MANAGEMENT TYPES AND STORAGE
// =================================================================

interface JobStatus {
  id: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  companyName: string;
  result?: object;
  error?: string;
  createdAt: Date;
  updatedAt: Date;
}

// In-memory job storage (in production, consider Redis or similar)
const jobs = new Map<string, JobStatus>();

// Clean up old jobs (older than 1 hour)
const cleanupOldJobs = () => {
  const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
  for (const [jobId, job] of jobs.entries()) {
    if (job.createdAt < oneHourAgo) {
      jobs.delete(jobId);
    }
  }
};

// Run cleanup every 30 minutes
setInterval(cleanupOldJobs, 30 * 60 * 1000);

// =================================================================
// CONFIGURATION
// =================================================================

/**
 * Loads, validates, and returns application configuration from environment variables.
 * Exits the process if a required variable is missing.
 */
const getAppConfig = () => {
  dotenv.config();

  const requiredEnvVars = ['LOGIN_EMAIL', 'LOGIN_PASSWORD', 'GEMINI_API_KEY', 'TAVILY_API_KEY'];

  for (const varName of requiredEnvVars) {
    if (!process.env[varName]) {
      console.error(`FATAL: Missing required environment variable: ${varName}`);
      process.exit(1); // Fail fast if configuration is incomplete
    }
  }

  return {
    port: process.env.PORT || 3001,
    login: {
      email: process.env.LOGIN_EMAIL!,
      password: process.env.LOGIN_PASSWORD!,
    },
    gemini: {
      apiKey: process.env.GEMINI_API_KEY!,
      modelId: process.env.MODEL_ID || 'gemini-2.5-flash', // Using a standard, recent model
    },
    tavily: {
      apiKey: process.env.TAVILY_API_KEY!,
    },
  };
};

const CONFIG = getAppConfig();

// =================================================================
// REACT PARADIGM TYPES AND INTERFACES
// =================================================================

interface SearchResult {
  title: string;
  url: string;
  content: string;
  score: number;
}

interface TavilyResponse {
  query: string;
  results: SearchResult[];
}

interface ReActIteration {
  reasoning: string;
  action: string;
  query: string;
  observation: string;
  information_gathered: any;
  dataType?: string;
}

interface CompanyInformation {
  basic_info: any;
  products_and_services: string;
  management_profile: any[];
  group_structure: any;
  esg_initiatives: any;
  growth_metrics: any;
  industry_insights: any[];
  recent_news: any[];
  recent_socmed_posts: any[];
}

// =================================================================
// HELPER FUNCTIONS
// =================================================================

/**
 * Reads a prompt file from the './server' directory relative to the project root.
 * @param fileName The name of the file to read (e.g., 'system_prompt.md').
 * @returns The content of the file as a string.
 */
const readPromptFile = (fileName: string): string => {
  try {
    if (process.env.NODE_ENV === 'production') {
      const filePath = path.join(process.cwd(), 'prompts', fileName);
      return fs.readFileSync(filePath, 'utf8');
    }

    const filePath = path.join(process.cwd(), 'server', 'prompts', fileName);
    return fs.readFileSync(filePath, 'utf8');
  } catch (error) {
    console.error(`Error reading prompt file at ${path.join('server', fileName)}:`, error);
    throw new Error(`Could not read instruction file: ${fileName}`);
  }
};

/**
 * Calls the Tavily API to perform web searches.
 * @param query The search query string.
 * @returns A promise that resolves to the search results.
 */
const callTavilyAPI = async (query: string): Promise<TavilyResponse> => {
  const { apiKey } = CONFIG.tavily;
  const url = 'https://api.tavily.com/search';

  const requestBody = {
    query: query,
    search_depth: "advanced",
    include_answer: false,
    include_raw_content: false,
    max_results: 10,
    include_domains: [],
    exclude_domains: []
  };

  try {
    console.log(`Calling Tavily API with query: "${query}"`);
    const response = await axios.post(url, requestBody, {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey}`
      }
    });

    if (!response.data || !response.data.results) {
      throw new Error('Invalid response from Tavily API');
    }

    console.log(`Tavily API returned ${response.data.results.length} results`);
    return response.data;
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error calling Tavily API:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error in callTavilyAPI:', error);
    }
    throw error;
  }
};

/**
 * Extracts a JSON object from a string, which may be wrapped in markdown code blocks.
 * @param text The string potentially containing the JSON.
 * @returns The parsed JavaScript object.
 */
const extractAndParseJson = (text: string): object => {
  // Regex to find JSON in a markdown block ```json ... ``` or as a raw object { ... }.
  const jsonMatch = text.match(/```json\s*([\s\S]*?)\s*```|(\{[\s\S]*\})/);

  if (!jsonMatch) {
    console.error("Raw text from AI that failed parsing:", text);
    throw new Error('No valid JSON object found in the response from the AI model.');
  }

  // The first capturing group is for the markdown block, the second for the raw object.
  const jsonString = jsonMatch[1] || jsonMatch[2];

  try {
    return JSON.parse(jsonString);
  } catch (parseError) {
    console.error('Failed to parse the following JSON string:', jsonString);
    throw new Error('The AI model returned a malformed JSON object.');
  }
};

/**
 * Calls the Google Gemini API with a given prompt and system instruction.
 * @param systemInstruction The system-level instructions for the model.
 * @param userPrompt The user's prompt for this specific task.
 * @param searchResults Optional search results to include in the context.
 * @returns A promise that resolves to the parsed JSON object from the model's response.
 */
const callGemini = async (systemInstruction: string, userPrompt: string, searchResults?: SearchResult[]): Promise<object> => {
  const { apiKey, modelId } = CONFIG.gemini;
  const url = `https://generativelanguage.googleapis.com/v1beta/models/${modelId}:generateContent?key=${apiKey}`;

  // Include search results in the prompt if provided
  let enhancedPrompt = userPrompt;
  if (searchResults && searchResults.length > 0) {
    const searchContext = searchResults.map((result, index) =>
      `[Search Result ${index + 1}]\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\n`
    ).join('\n');

    enhancedPrompt = `${userPrompt}\n\n--- SEARCH RESULTS ---\n${searchContext}`;
  }

  const requestBody = {
    contents: [{ role: "user", parts: [{ text: enhancedPrompt }] }],
    generationConfig: {
      temperature: 0.4,
      thinkingConfig: { thinkingBudget: 24576 },
    },
    system_instruction: { parts: [{ text: systemInstruction }] },
    // Removed Google Search tools - we'll use Tavily instead
  };

  try {
    console.log(`Calling Gemini model ${modelId}...`);
    const response = await axios.post(url, requestBody, {
      headers: { 'Content-Type': 'application/json' }
    });

    const generatedContent = response.data.candidates?.[0]?.content?.parts?.[0]?.text;
    console.log("Raw Gemini Response:", generatedContent?.substring(0, 500) + "...");

    if (!generatedContent) {
      console.error("Raw Gemini Response:", JSON.stringify(response.data));
      throw new Error('No content was generated by the Gemini API.');
    }

    return extractAndParseJson(generatedContent);
  } catch (error) {
    if (axios.isAxiosError(error)) {
      console.error('Axios error calling Gemini API:', error.response?.data || error.message);
    } else {
      console.error('Unexpected error in callGemini:', error);
    }
    // Re-throw the error to be caught by the route handler
    throw error;
  }
};

// =================================================================
// REACT PARADIGM IMPLEMENTATION
// =================================================================

/**
 * Maps data types to their corresponding field names in CompanyInformation.
 * @param dataType The data type to map.
 * @returns The corresponding field name.
 */
const getFieldForDataType = (dataType: string): string => {
  const mapping: { [key: string]: string } = {
    'news': 'recent_news',
    'social_media': 'recent_socmed_posts',
    'basic': 'basic_info',
    'financial': 'financial_info',
    'leadership': 'management_profile',
    'additional': 'industry_insights'
  };
  return mapping[dataType] || 'basic_info';
};

/**
 * Performs reasoning to determine what information is still needed.
 * @param companyName The company being researched.
 * @param currentInfo The information gathered so far.
 * @param iteration The current iteration number.
 * @param previousQueries Array of previously executed queries to avoid duplicates.
 * @param failedDataTypes Set of data types that have been searched but not found.
 * @returns The reasoning and next action to take.
 */
const performReasoning = async (
  companyName: string,
  currentInfo: Partial<CompanyInformation>,
  iteration: number,
  previousQueries: string[] = [],
  failedDataTypes: Set<string> = new Set()
): Promise<{reasoning: string, action: string, query: string, dataType: string}> => {

  // Priority order: news and social media first, then other data
  const dataPriorities = [
    { type: 'news', field: 'recent_news', queries: [
      `${companyName} recent news latest developments`,
      `${companyName} news announcements press releases recent`,
      `${companyName} breaking news updates latest`
    ]},
    { type: 'social_media', field: 'recent_socmed_posts', queries: [
      `${companyName} social media posts LinkedIn Twitter Instagram Facebook`,
      `${companyName} official social media accounts recent posts`,
      `${companyName} social media presence updates`
    ]},
    { type: 'basic', field: 'basic_info', queries: [
      `${companyName} company basic information headquarters industry`,
      `${companyName} company overview business description`,
      `${companyName} company profile industry sector`
    ]},
    { type: 'financial', field: 'financial_info', queries: [
      `${companyName} financial information revenue business model`,
      `${companyName} funding investment financial performance`,
      `${companyName} revenue earnings financial results`
    ]},
    { type: 'leadership', field: 'management_profile', queries: [
      `${companyName} CEO leadership management team executives`,
      `${companyName} founders leadership team management`,
      `${companyName} executive team CEO CTO CFO`
    ]}
  ];

  // Smart reasoning: Find the next priority data type to search for
  for (const priority of dataPriorities) {
    // Skip if this data type has failed before
    if (failedDataTypes.has(priority.type)) {
      continue;
    }

    // Check if we already have sufficient data for this type
    const currentData = (currentInfo as any)[priority.field];
    const hasData = currentData && (Array.isArray(currentData) ? currentData.length > 0 : Object.keys(currentData).length > 0);

    if (!hasData) {
      // Find a query that hasn't been used before
      for (const query of priority.queries) {
        const isSimilarQuery = previousQueries.some(prevQuery =>
          prevQuery.toLowerCase().includes(query.split(' ').slice(1, 3).join(' ').toLowerCase())
        );

        if (!isSimilarQuery) {
          return {
            reasoning: `Missing ${priority.type} data. Need to search for ${priority.field} information.`,
            action: 'search',
            query,
            dataType: priority.type
          };
        }
      }
    }
  }

  // If we reach here, either all priority data is collected or all queries have been tried
  // Use AI reasoning as fallback

  const reasoningPrompt = `
You are a business research analyst using the ReAct (Reasoning and Acting) paradigm to gather comprehensive company information.

Company: ${companyName}
Current Iteration: ${iteration}
Previous Queries: ${JSON.stringify(previousQueries)}
Failed Data Types (no data found): ${JSON.stringify(Array.from(failedDataTypes))}

Current Information Gathered:
${JSON.stringify(currentInfo, null, 2)}

PRIORITY: Focus on recent_news and recent_socmed_posts first, as these are the most important data types.

Based on the current information, reason about what specific information is still missing or incomplete.
Avoid searching for data types that are in the Failed Data Types list.
Do not repeat queries that are similar to those in Previous Queries.

Your response should be in this exact format:
REASONING: [Explain what information is missing and why the next search is needed]
ACTION: search
QUERY: [Specific search query to fill the information gap]
DATA_TYPE: [One of: news, social_media, basic, financial, leadership, additional]

Focus on fulfilling the following structured JSON format:
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  }
}

Make your search query targeted to get the most relevant results.
`;

  try {
    const response = await axios.post(`https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash:generateContent?key=${CONFIG.gemini.apiKey}`, {
      contents: [{ role: "user", parts: [{ text: reasoningPrompt }] }],
      generationConfig: { temperature: 0.3 }
    }, {
      headers: { 'Content-Type': 'application/json' }
    });

    const content = response.data.candidates?.[0]?.content?.parts?.[0]?.text || '';

    // Parse the structured response
    const reasoningMatch = content.match(/REASONING:\s*(.*?)(?=ACTION:|$)/s);
    const actionMatch = content.match(/ACTION:\s*(.*?)(?=QUERY:|$)/s);
    const queryMatch = content.match(/QUERY:\s*(.*?)(?=DATA_TYPE:|$)/s);
    const dataTypeMatch = content.match(/DATA_TYPE:\s*(.*?)$/s);

    const reasoning = reasoningMatch?.[1]?.trim() || 'Continuing research to gather comprehensive company information.';
    const action = actionMatch?.[1]?.trim() || 'search';
    const query = queryMatch?.[1]?.trim() || `${companyName} company information`;
    const dataType = dataTypeMatch?.[1]?.trim() || 'additional';

    return { reasoning, action, query, dataType };
  } catch (error) {
    console.error('Error in performReasoning:', error);
    // Fallback reasoning based on iteration
    const fallbackQueries = [
      `${companyName} company basic information headquarters industry`,
      `${companyName} recent news developments`,
      `${companyName} financial information business model revenue`,
      `${companyName} CEO leadership management team executives`,
      `${companyName} products services offerings customers`
    ];

    const query = fallbackQueries[Math.min(iteration - 1, fallbackQueries.length - 1)];
    const fallbackDataTypes = ['basic', 'news', 'financial', 'leadership', 'additional'];
    const dataType = fallbackDataTypes[Math.min(iteration - 1, fallbackDataTypes.length - 1)];

    return {
      reasoning: `Iteration ${iteration}: Gathering information about ${companyName}`,
      action: 'search',
      query,
      dataType
    };
  }
};

/**
 * Performs the search action using Tavily API.
 * @param query The search query.
 * @returns The search results and observation.
 */
const performAction = async (query: string): Promise<{results: SearchResult[], observation: string}> => {
  try {
    const tavilyResponse = await callTavilyAPI(query);
    const results = tavilyResponse.results || [];

    const observation = `Found ${results.length} search results. Key sources include: ${
      results.slice(0, 3).map(r => r.title).join(', ')
    }`;

    return { results, observation };
  } catch (error) {
    console.error('Error in performAction:', error);
    return {
      results: [],
      observation: `Search failed: ${error instanceof Error ? error.message : 'Unknown error'}`
    };
  }
};

// =================================================================
// BACKGROUND PROCESSING
// =================================================================

/**
 * Validates URLs to ensure they are accessible and not hallucinated.
 * @param urls Array of URLs to validate.
 * @returns Array of validated URLs.
 */
const validateUrls = async (urls: string[]): Promise<string[]> => {
  const validUrls: string[] = [];

  for (const url of urls) {
    try {
      // Basic URL format validation
      new URL(url);

      // Check if URL is accessible (with timeout)
      const response = await axios.head(url, {
        timeout: 5000,
        validateStatus: (status) => status < 500 // Accept redirects and client errors, but not server errors
      });

      if (response.status < 400) {
        validUrls.push(url);
      }
    } catch (error) {
      console.log(`URL validation failed for: ${url}`);
    }
  }

  return validUrls;
};

/**
 * Cross-references information across multiple search results to verify accuracy.
 * @param searchResults The search results to cross-reference.
 * @param companyName The company name for context.
 * @returns Verified information with confidence scores.
 */
const crossReferenceInformation = (searchResults: SearchResult[], companyName: string): {
  verifiedFacts: string[];
  suspiciousFacts: string[];
  urls: string[];
} => {
  const factCounts = new Map<string, number>();
  const urlSet = new Set<string>();

  // Extract URLs from search results
  searchResults.forEach(result => {
    urlSet.add(result.url);

    // Extract potential facts (simplified approach)
    const content = result.content.toLowerCase();
    const companyLower = companyName.toLowerCase();

    // Look for common fact patterns
    const patterns = [
      /founded in (\d{4})/g,
      /established in (\d{4})/g,
      /headquarters in ([^.]+)/g,
      /based in ([^.]+)/g,
      /(\d+) employees/g,
      /revenue of \$([^.]+)/g
    ];

    patterns.forEach(pattern => {
      const matches = content.match(pattern);
      if (matches) {
        matches.forEach(match => {
          const count = factCounts.get(match) || 0;
          factCounts.set(match, count + 1);
        });
      }
    });
  });

  // Separate verified facts (mentioned in multiple sources) from suspicious ones
  const verifiedFacts: string[] = [];
  const suspiciousFacts: string[] = [];

  factCounts.forEach((count, fact) => {
    // if (count >= 2) {
    //   verifiedFacts.push(fact);
    // } else {
    //   suspiciousFacts.push(fact);
    // }
    verifiedFacts.push(fact);
  });

  return {
    verifiedFacts,
    suspiciousFacts,
    urls: Array.from(urlSet)
  };
};

/**
 * Extracts and processes information from search results using AI with verification.
 * @param companyName The company name.
 * @param searchResults The search results to process.
 * @param currentInfo The current information gathered.
 * @param queryType The type of query (basic, news, financial, etc.).
 * @returns The extracted information.
 */
const extractInformationFromResults = async (
  companyName: string,
  searchResults: SearchResult[],
  currentInfo: Partial<CompanyInformation>,
  queryType: string
): Promise<Partial<CompanyInformation>> => {
  // Cross-reference information for verification
  const { verifiedFacts, urls } = crossReferenceInformation(searchResults, companyName);

  // Validate URLs
  // const validUrls = await validateUrls(urls);
  const validUrls = urls;

  const extractionPrompt = `
You are a business research analyst extracting specific information about ${companyName} from search results.

Query Type: ${queryType}
Current Information: ${JSON.stringify(currentInfo, null, 2)}

Search Results:
${searchResults.map((result, index) =>
  `[Result ${index + 1}]\nTitle: ${result.title}\nURL: ${result.url}\nContent: ${result.content}\n`
).join('\n')}

Verified Facts (mentioned in multiple sources):
${verifiedFacts.join('\n')}

Valid URLs Available:
${validUrls.join('\n')}

CRITICAL VERIFICATION RULES:
1. Only use URLs from the "Valid URLs Available" list above
2. Prioritize information that appears in multiple search results
3. Cross-reference dates, numbers, and facts across sources
4. If information conflicts between sources, note the discrepancy
5. Do not generate or hallucinate any URLs not in the valid list

Extract and return ONLY new information that is not already in the current information. Focus on:
- Verifying and updating existing information with more accurate data
- Adding missing information relevant to the query type
- Including only URLs that are in the Valid URLs Available list
- Using exact dates and figures when available

Return the information in this JSON structure (include only fields with new information):
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  },
  "has_new_information": "boolean (true if there is new information, false otherwise)"
}

IMPORTANT: Only include URLs that are in the Valid URLs Available list above. Any URL not in that list will be considered hallucinated and invalid.
`;

  try {
    const response = await callGemini('You are a precise business research analyst. Extract only verified information from the provided search results. Never hallucinate URLs.', extractionPrompt);
    return response as Partial<CompanyInformation>;
  } catch (error) {
    console.error('Error extracting information:', error);
    return {};
  }
};

/**
 * Merges new information with existing information.
 * @param current The current information.
 * @param newInfo The new information to merge.
 * @returns The merged information.
 */
const mergeInformation = async (current: Partial<CompanyInformation>, newInfo: Partial<CompanyInformation>): Promise<Partial<CompanyInformation>> => {
  // Use gemini to merge the information
  const prompt = `
You are a precise business research analyst. Merge the new information with the existing information.

Existing Information: ${JSON.stringify(current, null, 2)}
New Information: ${JSON.stringify(newInfo, null, 2)}

Return the merged information in this JSON structure:
{
  "company_profile": {
    "basic_info": {
      "company_name": "string",
      "logo_url": "string (if available)",
      "industry_category": "string (e.g., FMCG - snack & confectionery manufacturing)",
      "description": "string (2-3 sentences describing the business)",
      "location": "string (City, Region/State)",
      "employee_count": "string (e.g., 1,200 employees)",
      "established_year": "string (e.g., Est. 2000)",
      "website": "string (company website URL)"
    },
    "products_and_services": "string (few sentences explaining what the company offers as products/services)",
    "management_profile": {
      "name": "string",
      "position": "string (e.g., Founder & CEO, Operations Manager)",
      "background": "string (professional background description)"
    }[],
    "group_structure": {
      "parent_company": "string",
      "subsidiaries": {
        "name": "string",
        "description": "string"
      }[],
      "ownership_structure": "string (e.g., privately held, public equity details)"
    },
    "esg_initiatives": {
      "environmental": "string (environmental initiatives)"[],
      "social": "string (social responsibility programs)"[],
      "governance": "string (governance practices)"[]
    },
    "growth_metrics": {
      "cagr": {
        "percentage": "float (e.g., 0.39 for 39%)",
        "benchmark": "string (e.g., Industry Growth)",
        "data_points": {
          "year": "number",
          "value": "float (e.g., 2000000000.0, 4000000000.0)"
        }[]
      }
    },
    "industry_insights": {
      "title": "string",
      "description": "string (insights about the industry the company operates in, not about the company itself)"
    }[],
    "recent_news": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "categories": ["string", "string"],
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the news article)"
    }[],
    "recent_socmed_posts": {
      "title": "string",
      "date": "string (DD Month YYYY format)",
      "socmed_type": "string (enum: x, instagram, linkedin, facebook, youtube)",
      "summary": "string (brief summary)",
      "source_url": "string (valid URL link to the social media post)"
    }[]
  }
}

IMPORTANT: Your final response must be the complete JSON object with no additional text afterward. The JSON should be properly formatted and valid.
`;

  try {
    const response = await callGemini('You are a precise business research analyst. Merge the new information with the existing information.', prompt);
    return response as Partial<CompanyInformation>;
  } catch (error) {
    console.error('Error merging information:', error);
    return current;
  }
};

const checkSufficientInformation = async (currentInfo: Partial<CompanyInformation>): Promise<boolean> => {
  const prompt = `
You are a precise business research analyst. Evaluate the completeness of given company profile information.

Return true if the company profile is complete and contains all the necessary information. Otherwise, return false.

You must return in valid JSON format.
{
  "is_complete": boolean
}
`;

  try {
    const response = await callGemini(`Please evaluate the completeness of the following company profile information: ${JSON.stringify(currentInfo, null, 2)}`, prompt);
    const result = response as { is_complete?: boolean };
    return result.is_complete ?? false;
  } catch (error) {
    console.error('Error checking information completeness:', error);
    return false;
  }
};

/**
 * Processes company profiling using ReAct paradigm with multiple search iterations.
 */
const processCompanyProfilingInBackground = async (jobId: string, companyName: string) => {
  try {
    // Update job status to processing
    const job = jobs.get(jobId);
    if (!job) return;

    job.status = 'processing';
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`\n--- Starting ReAct-based profiling for: ${companyName} (Job ID: ${jobId}) ---`);

    let currentInfo: Partial<CompanyInformation> = {};
    const iterations: ReActIteration[] = [];
    const maxIterations = 10;
    const previousQueries: string[] = [];
    const failedDataTypes: Set<string> = new Set();

    // ReAct Loop: Reason → Act → Observe → Plan
    for (let i = 1; i <= maxIterations; i++) {
      console.log(`\n--- ReAct Iteration ${i} ---`);

      // REASON: Determine what information is still needed
      const { reasoning, action, query, dataType } = await performReasoning(companyName, currentInfo, i, previousQueries, failedDataTypes);
      console.log(`Reasoning: ${reasoning}`);
      console.log(`Action: ${action}`);
      console.log(`Query: ${query}`);
      console.log(`Data Type: ${dataType}`);

      // Track this query to avoid duplicates
      previousQueries.push(query);

      // ACT: Perform the search
      const { results, observation } = await performAction(query);
      console.log(`Observation: ${observation}`);

      // OBSERVE: Extract information from search results
      const extractedInfo = await extractInformationFromResults(companyName, results, currentInfo, dataType);
      console.log('Extracted Info:', JSON.stringify(extractedInfo, null, 2));

      // Check if we got meaningful data for this data type
      const hasNewData = extractedInfo
      if (!hasNewData || (extractedInfo as any)[getFieldForDataType(dataType)] === undefined) {
        console.log(`No data found for ${dataType}, marking as failed`);
        failedDataTypes.add(dataType);
      }

      // Merge new information
      currentInfo = await mergeInformation(currentInfo, extractedInfo);
      console.log('Current Info:', JSON.stringify(currentInfo, null, 2));

      // Store iteration details
      iterations.push({
        reasoning,
        action,
        query,
        observation,
        information_gathered: extractedInfo,
        dataType
      });

      // Smart termination logic
      const hasBasicInfo = currentInfo.basic_info?.company_name && currentInfo.basic_info?.industry_category;
      const hasNews = currentInfo.recent_news && currentInfo.recent_news.length > 0;
      const hasSocialMedia = currentInfo.recent_socmed_posts && currentInfo.recent_socmed_posts.length > 0;

      // Priority check: If we have news and social media (high priority), we can be more lenient
      if (i >= 3 && hasBasicInfo && (hasNews || hasSocialMedia)) {
        console.log(`Priority information gathered after ${i} iterations. News: ${hasNews}, Social Media: ${hasSocialMedia}`);
        break;
      }

      // Check if all priority data types have been attempted (either found or failed)
      const priorityDataTypes = ['news', 'social_media', 'basic'];
      const allPriorityAttempted = priorityDataTypes.every(type =>
        failedDataTypes.has(type) ||
        (currentInfo as any)[getFieldForDataType(type)] !== undefined
      );

      if (i >= 5 && allPriorityAttempted) {
        console.log(`All priority data types attempted after ${i} iterations.`);
        break;
      }

      // Fallback to AI-based completeness check for later iterations
      if (i >= 7) {
        const hasSufficientInfo = await checkSufficientInformation(currentInfo);
        if (hasSufficientInfo) {
          console.log(`Sufficient information gathered after ${i} iterations.`);
          break;
        }
      }
    }

    // Final processing: Create complete profile using system prompt
    console.log("\n--- Final Profile Generation ---");
    const systemInstruction = readPromptFile('system_prompt.md');
    const finalPrompt = `
**Company to Research**: ${companyName}

**Information Gathered from ReAct Research**:
${JSON.stringify(currentInfo, null, 2)}

**Research Iterations**:
${iterations.map((iter, index) =>
  `Iteration ${index + 1}:\n- Reasoning: ${iter.reasoning}\n- Query: ${iter.query}\n- Observation: ${iter.observation}`
).join('\n\n')}

Using the above research data, create a comprehensive company profile in the exact JSON format specified in the system instructions. Ensure all URLs are valid and came from the research results above.
`;

    const finalProfile = await callGemini(systemInstruction, finalPrompt);

    // Update job with successful result
    job.status = 'completed';
    job.result = finalProfile;
    job.updatedAt = new Date();
    jobs.set(jobId, job);

    console.log(`--- ReAct profiling for ${companyName} completed successfully (Job ID: ${jobId}). ---`);
  } catch (error) {
    // Update job with error
    const job = jobs.get(jobId);
    if (job) {
      job.status = 'failed';
      job.error = error instanceof Error ? error.message : 'An unknown error occurred';
      job.updatedAt = new Date();
      jobs.set(jobId, job);
    }

    console.error(`--- ReAct profiling for ${companyName} failed (Job ID: ${jobId}). ---`);
    console.error('Error:', error);
  }
};

// =================================================================
// ROUTE HANDLERS
// =================================================================

/**
 * Handles user login requests.
 */
const handleLogin = (req: Request, res: Response) => {
  const { email, password } = req.body;

  if (email === CONFIG.login.email && password === CONFIG.login.password) {
    res.json({
      success: true,
      message: 'Login successful',
      token: 'dummy-jwt-token', // In production, use a proper JWT library
    });
  } else {
    res.status(401).json({
      success: false,
      message: 'Invalid email or password',
    });
  }
};

/**
 * Handles company profiling requests by starting a background job.
 */
const handleCompanyProfiling = async (req: Request, res: Response) => {
  const { companyName } = req.body;
  if (!companyName) {
    return res.status(400).json({ success: false, message: 'Company name is required' });
  }

  // Create a new job
  const jobId = uuidv4();
  const job: JobStatus = {
    id: jobId,
    status: 'pending',
    companyName: companyName.trim(),
    createdAt: new Date(),
    updatedAt: new Date(),
  };

  jobs.set(jobId, job);

  // Start background processing (don't await)
  processCompanyProfilingInBackground(jobId, companyName.trim()).catch(error => {
    console.error('Background processing error:', error);
  });

  // Return immediately with job ID
  res.json({
    success: true,
    jobId: jobId,
    message: 'Company profiling started. Use the job ID to check status.',
  });
};

/**
 * Handles job status requests.
 */
const handleJobStatus = (req: Request, res: Response) => {
  const { jobId } = req.params;

  if (!jobId) {
    return res.status(400).json({ success: false, message: 'Job ID is required' });
  }

  const job = jobs.get(jobId);

  if (!job) {
    return res.status(404).json({ success: false, message: 'Job not found' });
  }

  // Return job status without internal details
  const response: {
    success: boolean;
    jobId: string;
    status: string;
    companyName: string;
    createdAt: Date;
    updatedAt: Date;
    data?: object;
    error?: string;
  } = {
    success: true,
    jobId: job.id,
    status: job.status,
    companyName: job.companyName,
    createdAt: job.createdAt,
    updatedAt: job.updatedAt,
  };

  if (job.status === 'completed' && job.result) {
    response.data = job.result;
  }

  if (job.status === 'failed' && job.error) {
    response.error = job.error;
  }

  res.json(response);
};

/**
 * Handles health check requests.
 */
const handleHealthCheck = (_req: Request, res: Response) => {
  res.json({ status: 'OK', timestamp: new Date().toISOString() });
};

// =================================================================
// EXPRESS APP SETUP
// =================================================================

const app = express();

// Middleware
app.use(cors());
app.use(express.json());

// API Routes
app.post('/login', handleLogin);
app.post('/company_profiling', handleCompanyProfiling);
app.get('/job/:jobId', handleJobStatus);
app.get('/health', handleHealthCheck);

// =================================================================
// SERVER START
// =================================================================

app.listen(CONFIG.port, () => {
  console.log(`Server running on port ${CONFIG.port}`);
});
